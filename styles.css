/* CSS Custom Properties */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #4facfe;
    --text-dark: #2c3e50;
    --text-light: #ffffff;
    --text-muted: #6c757d;
    
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    --container-max-width: 1200px;
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    --font-primary: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Header & Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    transition: var(--transition);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand h2 {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--primary-gradient);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    animation: float 6s ease-in-out infinite;
}

.floating-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    text-align: center;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.gradient-text {
    background: linear-gradient(135deg, #ffffff 0%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    font-weight: var(--font-weight-medium);
    margin-bottom: 1.5rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.7s forwards;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.9s forwards;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.1s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    text-align: center;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--glass-bg);
    color: var(--text-light);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

.btn-secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--text-light);
}

.btn-secondary:hover {
    background: var(--text-light);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--glass-shadow);
    border-color: var(--primary-color);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    text-align: left;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-muted);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

/* Calculator Section */
.calculator-section {
    background: var(--dark-gradient);
    color: var(--text-light);
}

.calculator-section .section-title {
    color: var(--text-light);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.calculator-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.calculator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.calculator-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-light);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    color: var(--text-light);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-range {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--glass-bg);
    outline: none;
    -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--accent-gradient);
    cursor: pointer;
}

.range-value {
    display: block;
    text-align: center;
    margin-top: 0.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--accent-color);
}

.calculator-results {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
}

.calculator-results h3 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--text-light);
}

.result-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.result-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem 1rem;
    border: 1px solid var(--glass-border);
}

.result-number {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.result-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.result-cta {
    margin-top: 2rem;
}

.result-cta p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--text-light);
}

/* Benefits Section */
.benefits {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--glass-shadow);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.benefit-card p {
    color: var(--text-muted);
    line-height: 1.6;
}

/* Contact Section */
.contact {
    background: var(--primary-gradient);
    color: var(--text-light);
}

.contact .section-title {
    color: var(--text-light);
}

.contact .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: var(--transition);
}

.contact-item:hover {
    transform: translateX(10px);
    box-shadow: var(--glass-shadow);
}

.contact-icon {
    font-size: 2rem;
    min-width: 60px;
    text-align: center;
}

.contact-item h4 {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.whatsapp-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
}

.whatsapp-link:hover {
    color: var(--text-light);
}

.contact-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    color: var(--text-light);
}

.contact-form .form-control:focus {
    border-color: var(--accent-color);
}

/* Footer */
.footer {
    background: var(--dark-gradient);
    color: var(--text-light);
    padding: 2rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-brand h3 {
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .calculator-container,
    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    section {
        padding: 60px 0;
    }
    
    .service-card,
    .benefit-card,
    .calculator-form,
    .calculator-results,
    .contact-form {
        padding: 1.5rem;
    }
    
    .result-cards {
        grid-template-columns: 1fr;
    }
}

/* Animation Classes */
[data-aos] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth Scrolling Enhancement */
html {
    scroll-padding-top: 70px;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Form Error Styles */
.form-control.error {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.error-message {
    color: #ff6b6b;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
}

.notification-success {
    background: #4CAF50;
    color: white;
}

.notification-error {
    background: #f44336;
    color: white;
}

.notification-info {
    background: #2196F3;
    color: white;
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: 1rem;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

/* Enhanced Button States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Mobile Menu Animation */
.nav-toggle.active span:nth-child(1) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(2) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Enhanced Scroll Animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease-out;
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s ease-out;
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .floating-element,
    .notification {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .hero {
        background: none;
        color: black;
    }

    .section-title,
    .hero-title {
        color: black !important;
        -webkit-text-fill-color: black !important;
    }
}
