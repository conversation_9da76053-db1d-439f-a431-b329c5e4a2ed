// Meeem Digital Marketing Agency - Interactive JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// Initialize all website functionality
function initializeWebsite() {
    setupNavigation();
    setupScrollAnimations();
    setupFormHandling();
    setupGrowthCalculator();
    setupSmoothScrolling();
    setupMobileMenu();
    setupHeaderScroll();
}

// Navigation Setup
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Close mobile menu if open
                const navMenu = document.querySelector('.nav-menu');
                navMenu.classList.remove('active');
            }
        });
    });
}

// Mobile Menu Setup
function setupMobileMenu() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Animate hamburger menu
            const spans = this.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transform = navMenu.classList.contains('active') 
                    ? `rotate(${index === 1 ? 45 : index === 2 ? -45 : 0}deg) translate(${index === 1 ? '5px, 5px' : index === 2 ? '-5px, 5px' : '0'})`
                    : 'none';
                span.style.opacity = index === 0 && navMenu.classList.contains('active') ? '0' : '1';
            });
        });
    }
}

// Header Scroll Effect
function setupHeaderScroll() {
    const header = document.querySelector('.header');
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', function() {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.1)';
            header.style.boxShadow = 'none';
        }
        
        lastScrollY = currentScrollY;
    });
}

// Scroll Animations Setup
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('aos-animate');
            }
        });
    }, observerOptions);
    
    // Observe all elements with data-aos attribute
    const animatedElements = document.querySelectorAll('[data-aos]');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Growth Calculator Functionality
function setupGrowthCalculator() {
    const timeframeSlider = document.getElementById('timeframe');
    const rangeValue = document.querySelector('.range-value');
    
    if (timeframeSlider && rangeValue) {
        timeframeSlider.addEventListener('input', function() {
            const months = this.value;
            rangeValue.textContent = `${months} month${months > 1 ? 's' : ''}`;
        });
    }
}

// Growth Calculator Main Function
function calculateGrowth() {
    const platform = document.getElementById('platform').value;
    const currentFollowers = parseInt(document.getElementById('followers').value) || 0;
    const monthlyBudget = parseInt(document.getElementById('budget').value) || 0;
    const timeframe = parseInt(document.getElementById('timeframe').value) || 6;
    
    // Validation
    if (currentFollowers < 0 || monthlyBudget < 0) {
        alert('Please enter valid positive numbers for followers and budget.');
        return;
    }
    
    if (monthlyBudget < 10000) {
        alert('Minimum budget should be PKR 10,000 for effective results.');
        return;
    }
    
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.innerHTML = '<span class="loading"></span> Calculating...';
    button.disabled = true;
    
    // Simulate calculation delay for better UX
    setTimeout(() => {
        const results = performGrowthCalculation(platform, currentFollowers, monthlyBudget, timeframe);
        displayResults(results);
        
        // Reset button
        button.textContent = originalText;
        button.disabled = false;
    }, 1500);
}

// Growth Calculation Logic
function performGrowthCalculation(platform, currentFollowers, monthlyBudget, timeframe) {
    // Platform-specific growth rates and engagement rates
    const platformData = {
        instagram: {
            baseGrowthRate: 0.15, // 15% monthly growth
            budgetMultiplier: 0.00002, // Growth per PKR
            maxGrowthRate: 0.4, // Maximum 40% monthly growth
            engagementRate: 0.035, // 3.5% average engagement
            reachMultiplier: 25 // Reach multiplier
        },
        youtube: {
            baseGrowthRate: 0.12,
            budgetMultiplier: 0.000015,
            maxGrowthRate: 0.35,
            engagementRate: 0.045,
            reachMultiplier: 20
        },
        tiktok: {
            baseGrowthRate: 0.25,
            budgetMultiplier: 0.000025,
            maxGrowthRate: 0.6,
            engagementRate: 0.055,
            reachMultiplier: 35
        },
        facebook: {
            baseGrowthRate: 0.08,
            budgetMultiplier: 0.000012,
            maxGrowthRate: 0.25,
            engagementRate: 0.025,
            reachMultiplier: 15
        }
    };
    
    const data = platformData[platform];
    
    // Calculate monthly growth rate
    let monthlyGrowthRate = data.baseGrowthRate + (monthlyBudget * data.budgetMultiplier);
    monthlyGrowthRate = Math.min(monthlyGrowthRate, data.maxGrowthRate);
    
    // Calculate projected followers
    let projectedFollowers = currentFollowers;
    for (let i = 0; i < timeframe; i++) {
        projectedFollowers *= (1 + monthlyGrowthRate);
    }
    
    // Calculate engagement rate (decreases slightly with larger audiences)
    const audienceFactor = Math.max(0.7, 1 - (projectedFollowers / 1000000) * 0.3);
    const finalEngagementRate = data.engagementRate * audienceFactor;
    
    // Calculate monthly reach
    const monthlyReach = projectedFollowers * data.reachMultiplier;
    
    return {
        projectedFollowers: Math.round(projectedFollowers),
        engagementRate: (finalEngagementRate * 100).toFixed(1),
        monthlyReach: Math.round(monthlyReach)
    };
}

// Display Calculation Results
function displayResults(results) {
    const resultsSection = document.getElementById('results');
    const projectedFollowersEl = document.getElementById('projected-followers');
    const engagementRateEl = document.getElementById('engagement-rate');
    const monthlyReachEl = document.getElementById('monthly-reach');
    
    // Format numbers for display
    const formatNumber = (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };
    
    // Animate numbers counting up
    animateNumber(projectedFollowersEl, 0, results.projectedFollowers, formatNumber);
    animateNumber(engagementRateEl, 0, parseFloat(results.engagementRate), (num) => num.toFixed(1) + '%');
    animateNumber(monthlyReachEl, 0, results.monthlyReach, formatNumber);
    
    // Show results with animation
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    
    // Add success animation
    resultsSection.style.opacity = '0';
    resultsSection.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        resultsSection.style.transition = 'all 0.5s ease-out';
        resultsSection.style.opacity = '1';
        resultsSection.style.transform = 'translateY(0)';
    }, 100);
}

// Animate Number Counting
function animateNumber(element, start, end, formatter) {
    const duration = 2000; // 2 seconds
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = start + (end - start) * easeOutQuart;
        
        element.textContent = formatter(currentValue);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = formatter(end);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Form Handling
function setupFormHandling() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleContactForm(this);
        });
    }
    
    // Real-time form validation
    const formInputs = document.querySelectorAll('.form-control');
    formInputs.forEach(input => {
        input.addEventListener('blur', validateInput);
        input.addEventListener('input', clearValidationError);
    });
}

// Handle Contact Form Submission
function handleContactForm(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Show loading state
    const originalText = submitButton.textContent;
    submitButton.innerHTML = '<span class="loading"></span> Sending...';
    submitButton.disabled = true;
    
    // Simulate form submission (replace with actual API call)
    setTimeout(() => {
        // Success message
        showNotification('Thank you! Your message has been sent successfully. We\'ll get back to you soon!', 'success');
        form.reset();
        
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }, 2000);
}

// Input Validation
function validateInput(event) {
    const input = event.target;
    const value = input.value.trim();
    
    // Remove existing error styling
    input.classList.remove('error');
    
    // Validation rules
    if (input.hasAttribute('required') && !value) {
        showInputError(input, 'This field is required');
        return false;
    }
    
    if (input.type === 'email' && value && !isValidEmail(value)) {
        showInputError(input, 'Please enter a valid email address');
        return false;
    }
    
    if (input.type === 'number' && value && isNaN(value)) {
        showInputError(input, 'Please enter a valid number');
        return false;
    }
    
    return true;
}

// Clear Validation Error
function clearValidationError(event) {
    const input = event.target;
    input.classList.remove('error');
    
    const errorMessage = input.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Show Input Error
function showInputError(input, message) {
    input.classList.add('error');
    
    // Remove existing error message
    const existingError = input.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    errorDiv.style.color = '#ff6b6b';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    
    input.parentNode.appendChild(errorDiv);
}

// Email Validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Styling
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
        max-width: 400px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Close button functionality
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        removeNotification(notification);
    });
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);
}

// Remove Notification
function removeNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Smooth Scrolling Setup
function setupSmoothScrolling() {
    // Add smooth scrolling to all anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href === '#') {
                e.preventDefault();
                return;
            }
            
            const targetElement = document.querySelector(href);
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Performance Optimization
const debouncedResize = debounce(() => {
    // Handle resize events
    console.log('Window resized');
}, 250);

const throttledScroll = throttle(() => {
    // Handle scroll events
}, 16); // ~60fps

window.addEventListener('resize', debouncedResize);
window.addEventListener('scroll', throttledScroll);

// Error Handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You could send this to an error tracking service
});

// Console Welcome Message
console.log(`
🚀 Welcome to Meeem Digital Marketing Agency
💼 Premium digital marketing solutions
📱 Social Media Management | 🎬 Video Editing | 📈 Growth Strategies
🌐 Visit: https://meeem.agency
`);

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        calculateGrowth,
        performGrowthCalculation,
        isValidEmail,
        formatNumber: (num) => {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
    };
}
